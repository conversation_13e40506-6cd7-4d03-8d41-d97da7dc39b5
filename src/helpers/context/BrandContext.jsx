import { createContext, useContext, useState, useEffect } from "react";
import { URL } from "../constant/Url";
import apiInstance from "../Axios/axiosINstance";
import siteConstant from "../constant/siteConstant";
import { fetchFromStorage } from "./storage";

const BrandContext = createContext();

export const BrandProvider = ({ children }) => {
  const [selectedBrand, setSelectedBrand] = useState(null);
  const [brands, setBrands] = useState([]);
  const [loadingBrand, setLoadingBrand] = useState(true);
  const [brandsLoaded, setBrandsLoaded] = useState(false);
  const [userToken, setUserToken] = useState(null);

  // Effect to track user token changes
  useEffect(() => {
    const checkToken = () => {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const token = userData?.token;

      // If token changes or is removed, reset brand selection
      if (token !== userToken) {
        console.log("Token changed, resetting brand selection");
        setUserToken(token);
        if (!token) {
          // User logged out - reset everything
          setSelectedBrand(null);
          setBrands([]);
          setBrandsLoaded(false);
          localStorage.removeItem("BrandId");
        } else {
          // User logged in - trigger brand fetch
          setBrandsLoaded(false);
          setLoadingBrand(true);
        }
      }
    };

    // Check immediately
    checkToken();

    // Set up interval to check for token changes
    const intervalId = setInterval(checkToken, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [userToken]); // Add userToken as dependency

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
        // Only fetch brands if the user is authenticated
        if (!userData?.token) return;

        setLoadingBrand(true);
        const response = await apiInstance.get(URL.GET_BRANDS);
        const fetchedBrands = response.data.data || [];
        console.log("Fetched brands from API:", fetchedBrands);
        setBrands(fetchedBrands);
        setBrandsLoaded(true);
      } catch (error) {
        console.error("Error fetching brands:", error);
      } finally {
        setLoadingBrand(false);
      }
    };

    if (userToken) {
      fetchBrands();
    }
  }, [userToken]); // Re-fetch brands when user token changes

  // Separate effect for brand selection that only runs after brands are loaded
  useEffect(() => {
    if (!brandsLoaded || !brands || brands.length === 0) {
      return;
    }

    console.log("Brands loaded, selecting brand...");
    console.log("Available brands:", brands);
    console.log(
      "Stored BrandId in localStorage:",
      localStorage.getItem("BrandId")
    );

    const storedBrandId = localStorage.getItem("BrandId");
    if (!storedBrandId) {
      console.log("No stored brand ID found");
      // Select first brand by default for new users
      const firstBrand = brands[0];
      if (firstBrand) {
        console.log("Selecting first brand:", firstBrand);
        setSelectedBrand(firstBrand);
        localStorage.setItem("BrandId", firstBrand.id.toString());
      }
      return;
    }

    const brandId = parseInt(storedBrandId, 10);
    console.log("Looking for brand with ID:", brandId);

    const brandToSelect = brands.find((b) => b.id === brandId);
    console.log("Found brand:", brandToSelect);

    if (brandToSelect) {
      console.log("Setting selected brand to:", brandToSelect);
      setSelectedBrand(brandToSelect);
    } else {
      console.log("Stored brand not found in available brands");
      // Only fall back to first brand if the stored brand is not found
      const firstBrand = brands[0];
      console.log("Falling back to first brand:", firstBrand);
      setSelectedBrand(firstBrand);
      localStorage.setItem("BrandId", firstBrand.id.toString());
    }
  }, [brandsLoaded, brands, userToken]); // Re-run when user token changes

  const handleBrandSelect = (brand) => {
    console.log("Handling brand selection:", brand);
    setSelectedBrand(brand);
    localStorage.setItem("BrandId", brand?.id?.toString());
  };

  const resetBrand = () => {
    setSelectedBrand(null);
    localStorage.removeItem("BrandId");
  };

  // Method to refresh brands after deletion
  const refreshBrands = async () => {
    try {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      if (!userData?.token) return;

      setLoadingBrand(true);
      const response = await apiInstance.get(URL.GET_BRANDS);
      const fetchedBrands = response.data.data || [];
      setBrands(fetchedBrands);
    } catch (error) {
      console.error("Error refreshing brands:", error);
    } finally {
      setLoadingBrand(false);
    }
  };

  return (
    <BrandContext.Provider
      value={{
        selectedBrand,
        handleBrandSelect,
        brands,
        setBrands,
        resetBrand,
        loadingBrand,
        refreshBrands,
      }}
    >
      {children}
    </BrandContext.Provider>
  );
};

export const useBrand = () => useContext(BrandContext);
